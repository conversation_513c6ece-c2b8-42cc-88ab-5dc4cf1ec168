import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { LuEye } from "react-icons/lu";
import { PiEyeSlash } from "react-icons/pi";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { useMutation } from "@tanstack/react-query";

import { signupSchema } from "@/features/auth/validation";
import GoogleButton from "@/components/ui/google-button";
import FingerPrintImage from "@/assets/fingerprint.png";
import { FormData } from "../type";
import { registerApi } from "../api";
import { showToast } from "@/lib/toast";
import { AxiosError } from "axios";
export function RegisterForm() {
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const navigate = useNavigate();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<FormData>({
    resolver: joiResolver(signupSchema),
  });
  const { mutate, isPending } = useMutation({
    mutationFn: registerApi,
    onSuccess: ({ data }) => {
      localStorage.setItem("userAuthId", data.auth._id);
      navigate("/verify-otp", {
        state: {
          email: data.auth.email,
        },
      });
    },
    onError: (error: AxiosError<Array<{ message?: string }>>) => {
      if (
        error.response?.status === 400 &&
        error.response?.data[0]?.message == "User already exist"
      ) {
        showToast("User already exists", "error");
        return;
      }
      showToast("Something went wrong", "error");
    },
  });
  const onSubmit = (data?: FormData) => {
    const payload = { email: data?.email, password: data?.password };
    mutate(payload);
  };

  return (
    <div className="w-full max-w-md space-y-6 text-center">
      <div className="space-y-2">
        <Link to={"/"}>
          <img
            src={FingerPrintImage}
            alt="Fingerprint"
            className="object-cover mx-auto"
          />
        </Link>
        <h1 className="text-3xl font-bold md:text-4xl font-prettywise">
          Welcome to Nurture
        </h1>
      </div>

      <GoogleButton text="Sign up with Google" />

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="px-2 bg-white text-muted-foreground">or</span>
        </div>
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-4 text-left">
          {/* Email Input */}
          <div>
            <Input
              className="border-input-border focus:border-slate-300"
              placeholder="Enter email"
              type="email"
              {...register("email")}
            />
            {errors.email && (
              <p className="text-sm text-red-500">
                {errors.email.message as string}
              </p>
            )}
          </div>

          {/* Password Input */}
          <div>
            <div className="flex border rounded-sm border-input-border focus:border-slate-300">
              <Input
                className="border-none"
                placeholder="Enter password"
                type={showPassword ? "text" : "password"}
                {...register("password")}
              />
              {showPassword ? (
                <PiEyeSlash
                  onClick={() => setShowPassword(false)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              ) : (
                <LuEye
                  onClick={() => setShowPassword(true)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              )}
            </div>
            {errors.password && (
              <p className="text-sm text-red-500">
                {errors.password.message as string}
              </p>
            )}
          </div>

          {/* Confirm Password Input */}
          <div>
            <div className="flex border rounded-sm border-input-border focus:border-slate-300">
              <Input
                className="border-none"
                placeholder="Re-enter password"
                type={showConfirmPassword ? "text" : "password"}
                {...register("confirmPassword")}
              />
              {showConfirmPassword ? (
                <PiEyeSlash
                  onClick={() => setShowConfirmPassword(false)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              ) : (
                <LuEye
                  onClick={() => setShowConfirmPassword(true)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              )}
            </div>
            {errors.confirmPassword && (
              <p className="text-sm text-red-500">
                {errors?.confirmPassword?.message as string}
              </p>
            )}
          </div>
        </div>

        <p className="mt-8 text-sm text-muted-foreground">
          By continuing, you agree to our{" "}
          <Link to="/terms" className="underline text-[#577DCF]">
            terms & conditions
          </Link>
        </p>

        <div className="flex flex-col mt-4 space-y-2 gap-y-3 md:gap-y-1">
          <Button
            disabled={isPending}
            type="submit"
            className="w-full hover:bg-[#c65a3c]"
          >
            {isPending ? "Registering..." : "Create account"}
          </Button>
          <Link to={"/"}>
            <Button variant="outline" disabled={isPending} className="w-full">
              Continue as guest
            </Button>
          </Link>
        </div>
      </form>
      <div className="flex justify-center gap-x-1">
        <p>Already have an account?</p>
        <Link to="/login" className="font-semibold underline text-orange-1">
          Log in
        </Link>
      </div>
    </div>
  );
}
