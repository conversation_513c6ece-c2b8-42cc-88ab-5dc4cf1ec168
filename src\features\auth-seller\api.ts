import api from "@/lib/axios";
import {
  forgotPasswordPayload,
  LoginPayload,
  OtpPayloadData,
  // ProductCreationPayload,
  RegisterPayload,
  resetPasswordPayload,
} from "./type";

export const registerApi = async (payload: RegisterPayload) => {
  const { data } = await api.post("/api/v1/auth/signup", payload);
  return data;
};

export const verifyOtpApi = async (payload: OtpPayloadData) => {
  const { data } = await api.post("/api/v1/auth/verify-otp", payload);
  return data;
};

export const setupProfileApi = async (payload: FormData) => {
  const { data } = await api.post("/api/v1/users/seller", payload, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return data;
};
export const sellerAddProductApi = async (payload: FormData) => {
  const { data } = await api.post("/api/v1/product", payload, {
    headers: {
      "Content-Type": "multipart/form-data",
    },
  });
  return data;
};

export const loginApi = async (payload: LoginPayload) => {
  const { data } = await api.post("/api/v1/auth/seller/login", payload);
  return data;
};

export const forgotPasswordApi = async (payload: forgotPasswordPayload) => {
  const { data } = await api.post("/api/v1/auth/forgot-password", payload);
  return data;
};

export const validateResetPasswordTokenApi = async (payload: string) => {
  const { data } = await api.get(`/api/v1/auth/validate-reset-token`, {
    params: {
      token: payload,
    },
  });
  return data;
};
export const resetPasswordApi = async (payload: resetPasswordPayload) => {
  const { data } = await api.post("/api/v1/auth/reset-password", payload);
  return data;
};

export const getSellerProductsApi = async (sellerId: string) => {
  const { data } = await api.get(`/api/v1/product/seller/${sellerId}`);
  return data;
};
export const deleteSellerProductsApi = async (payload: {
  sellerId: string;
  productId: string;
}) => {
  const { data } = await api.delete(
    `/api/v1/product/${payload.productId}/seller/${payload.sellerId}`
  );
  return data;
};
export const validateSellerApi = async (sellerId: string) => {
  const { data } = await api.patch(
    `/api/v1/users/seller/${sellerId}/validate`
  );
  return data;
};
