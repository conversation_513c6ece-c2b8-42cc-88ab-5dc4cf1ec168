import { useState } from "react";
import Layout from "@/components/layout/SellerLayout";
import OrderTable from "../components/OrderTable";
import OrderProductCard from "../components/OrderProductCard";
import { fetchSellerProductsApi } from "../api";
import { useQuery } from "@tanstack/react-query";
import { OrderTableItems } from "../type";
import OrderProductCardSkelton from "../components/OrderProductCardSkelton";

function SellerOrders() {
  const [currentPage, setCurrentPage] = useState(1);
  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };
  const {
    data,
    isPending: loading,
    isSuccess,
  } = useQuery({
    queryKey: ["orders", currentPage],
    queryFn: () => fetchSellerProductsApi(currentPage),
  });
  const totalPages = isSuccess ? data?.data?.totalPages : 1;

  return (
    <Layout showFooter={true}>
      <div className="p-4 mx-auto my-5 border rounded-lg border-gray-2">
        <div>
          <h1 className="text-lg font-bold">Orders</h1>
          <h4 className="text-neutral-300">Manage your orders</h4>
        </div>
        <div className="mt-5 hidden md:block max-h-[80vh] overflow-y-scroll scrollbar-hide">
          <OrderTable
            data={data?.data?.orders || []}
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            loading={loading}
          />
        </div>
        <div className="flex flex-col mt-5 md:hidden gap-y-3">
          {loading ? (
            [1, 2, 3].map((_, i) => <OrderProductCardSkelton key={i} />)
          ) : data?.data?.orders?.length === 0 ? (
            <OrderProductCardSkelton />
          ) : (
            data?.data?.orders.map((order: OrderTableItems) => (
              <OrderProductCard key={order._id} order={order} />
            ))
          )}
        </div>
      </div>
    </Layout>
  );
}

export default SellerOrders;
