import {
  fetchNotificationApi,
  markNotificationAsReadApi,
  Notification,
} from "@/api";
import { showToast } from "@/lib/toast";
import { dateFormatter, timeFormatter } from "@/lib/utils";
import { useMutation, useQuery } from "@tanstack/react-query";
import React, { useEffect, useState } from "react";
import { IoMdDoneAll } from "react-icons/io";
import { FaCaretDown } from "react-icons/fa";
import BeatLoader from "react-spinners/BeatLoader";

function NotificationSection({ nurtureUser }: { nurtureUser: string | null }) {
  const [selectedNotificationId, setSelectedNotificationId] = useState("");
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const notificationResult = useQuery({
    queryKey: ["notifications", currentPage],
    queryFn: () => fetchNotificationApi(currentPage),
    enabled: nurtureUser === "customer",
  });
  useEffect(() => {
    if (!notificationResult.data?.notifications) return;

    if (currentPage === 1) {
      setNotifications(notificationResult.data.notifications);
    } else {
      setNotifications((prev) => [
        ...prev,
        ...notificationResult.data.notifications,
      ]);
    }
  }, [notificationResult.data?.notifications, currentPage]);
  const { mutate, isPending: loading } = useMutation({
    mutationFn: markNotificationAsReadApi,
    onSuccess: () => {
      setNotifications((prev) => [
        ...prev.filter(
          (notification) => notification._id !== selectedNotificationId
        ),
      ]);
    },
    onError: () => {
      showToast("Failed to mark notification as read", "error");
    },
  });

  const handleMarkAsRead = (
    e: React.MouseEvent<HTMLDivElement, MouseEvent>,
    notificationId: string
  ) => {
    e.stopPropagation();
    if (loading) return;
    setSelectedNotificationId(notificationId);
    mutate(notificationId);
  };
  const loadMOre = (e: React.MouseEvent<HTMLHeadElement, MouseEvent>) => {
    e.stopPropagation();
    setCurrentPage((prev) => prev + 1);
  };
  return (
    <div
      className="absolute flex flex-col  bg-white border rounded-sm border-gray-100 
                top-36 left-1/2 transform -translate-x-1/2 w-10/12
                md:top-16 md:left-auto md:translate-x-0 md:right-0 md:w-[500px]"
    >
      <h1 className="p-4 text-lg font-semibold">Notifications</h1>

      <div className="max-h-[300px] w-full flex flex-col-reverse  overflow-y-scroll scrollbar-hide  md:flex-col gap-y-2 ">
        {notifications.length === 0 ? (
          <div className="mx-4 text-neutral-300">No new notifications.</div>
        ) : (
          <>
            {notifications.map((notification) => (
              <div
                key={notification._id + notification.createdAt}
                className="flex p-2 px-3 gap-x-2 "
              >
                <img
                  src={notification.image}
                  alt="notification"
                  className="w-12 h-12 rounded-md md:w-14 md:h-14"
                />
                <div className="flex flex-col justify-between">
                  <div className="text-sm md:text-base text-left text-neutral-600 md:max-w-[450px]">
                    {notification.message}
                  </div>

                  <div className="flex flex-wrap items-center w-full mt-1 text-xs text-neutral-300 gap-x-2 md:text-sm md:justify-between">
                    <div className="flex gap-x-2">
                      <p>{dateFormatter(notification.createdAt)}</p>
                      <span>|</span>
                      <p>{timeFormatter(notification.createdAt)}</p>
                    </div>

                    {/* Show this section only on md and above */}
                    <div
                      onClick={(e) => handleMarkAsRead(e, notification._id)}
                      className="hidden cursor-pointer md:flex gap-x-2"
                    >
                      <h4>Mark as read</h4>
                      {loading &&
                      selectedNotificationId === notification._id ? (
                        <div className="w-4 h-4 border-2 rounded-full border-coral border-t-transparent animate-spin"></div>
                      ) : (
                        <IoMdDoneAll className="my-auto text-lg text-orange-1" />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {notificationResult.isSuccess &&
              currentPage < notificationResult?.data?.totalPages && (
                <div className="flex justify-center mb-2 gap-x-2">
                  {notificationResult.isPending ? (
                    <BeatLoader
                      color="#d1795e"
                      size={10}
                      className="my-auto text-orange-1"
                    />
                  ) : (
                    <button className="flex justify-center w-full p-2 mx-4 border rounded-xl hover:opacity-70 gap-x-1" onClick={loadMOre}>
                      <h2>see more notifications</h2>
                      <FaCaretDown className="my-auto text-lg text-orange-1" />
                    </button>
                  )}
                </div>
              )}
          </>
        )}
      </div>
    </div>
  );
}

export default NotificationSection;
