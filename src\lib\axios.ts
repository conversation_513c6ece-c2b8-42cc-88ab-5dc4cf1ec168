import { useAuthStore } from "@/store/authStore";
import axios from "axios";

const baseURL =
  import.meta.env.VITE_API_ENV === "Development"
    ? import.meta.env.VITE_API_LOCAL_BASE_URL
    : import.meta.env.VITE_API_PROD_BASE_URL;

const api = axios.create({
  baseURL,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
  },
});

// Add auth token automatically
api.interceptors.request.use((config) => {
  const token = useAuthStore.getState().user?.accessToken;
  if (token) {
    config.headers.Authorization = `Bearer ${token}`;
  }

  return config;
});

// Handle expired tokens in response
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config;

    // access token expired
    if (error.response?.status === 403 && !originalRequest._retry) {
      originalRequest._retry = true;
      try {
        // Call the refresh token API
        const { data } = await api.post(`/api/v1/auth/refresh-token`, {
          role: localStorage.getItem("nurtureUser"),
        });
        const newAccessToken = data?.data?.accessToken;

        // Update the auth store with the new token
        useAuthStore.getState().login({
          accessToken: newAccessToken,
          role: localStorage.getItem("nurtureUser") as string,
        });
        api.defaults.headers.common["Authorization"] =
          `Bearer ${newAccessToken}`;
        return api(originalRequest);
      } catch (refreshError) {
        console.log(refreshError, "refresh errror");
      }
    }

    //refresh token expired
    if (
      error.response?.status === 401 &&
      error.response?.data[0].message === "Can't refresh token"
    ) {
      console.log("Token expired", error.response);
      // Prevent infinite retry loops
      useAuthStore.getState().authReset();
      window.location.href = "/";
    }

    return Promise.reject(error);
  }
);

export default api;
