import FingerPrintImage from "@/assets/fingerprint.png";
import FileUpload from "@/assets/file-upload.png";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { useMutation } from "@tanstack/react-query";
import { setupProfileSchema } from "../validation";
import { setupProfileApi } from "../api";
import { profileData } from "../type";
import { showToast } from "@/lib/toast";
import { Textarea } from "@/components/ui/textarea";
import { useState } from "react";
import { Separator } from "@/components/ui/separator";
import { useNavigate } from "react-router-dom";

function ProfileSetupForm() {
  const navigate = useNavigate();
  const [selectedFileName, setSelectedFileName] =
    useState<string>("Introductory video");
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<profileData>({
    resolver: joiResolver(setupProfileSchema),
  });

  // Update filename when video is selected
  const handleVideoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFileName(file.name);
    }
  };
  const { mutate, isPending } = useMutation({
    mutationFn: setupProfileApi,
    onSuccess: ({ data }) => {
      localStorage.setItem("sellerId", data.seller._id);
      navigate("product");
    },
    onError: () => {
      showToast("Something went wrong", "error");
    },
  });

  const onSubmit = (data: profileData) => {
    const authId = localStorage.getItem("userAuthId") as string;
    // localStorage.removeItem("userAuthId");
    const formData = new FormData();
    formData.append("companyName", data.companyName);
    formData.append("category", data.category);
    formData.append("phone", data.phone);
    formData.append("taxId", data.taxId);
    formData.append("refundPolicy", data.refundPolicy);
    formData.append("introductionVideo", data.video[0]);
    formData.append("authId", authId);
    mutate(formData);
  };

  return (
    <div className="flex flex-col items-center justify-center w-full max-w-md px-4 mx-auto space-y-5 text-center md:mx-0">
      <img
        src={FingerPrintImage}
        alt="Fingerprint"
        className="object-cover w-16 h-16 mx-auto"
      />
      <h1 className="text-2xl font-bold md:text-3xl font-prettywise">
        Setup your store
      </h1>
      <div className="w-full">
        <div className="flex justify-between">
          <p className="text-orange-1">Store details</p>
          <p className="text-neutral-100">Products</p>
        </div>
        <div className="flex justify-center w-9/12 mx-auto mt-2">
          <div className="my-auto rounded-full min-h-2 min-w-2 bg-orange-1"></div>
          <Separator className="w-full my-auto bg-neutral-300" />
        </div>
      </div>
      <form className="w-full space-y-4" onSubmit={handleSubmit(onSubmit)}>
        <div>
          <Input
            type="text"
            placeholder="Company Name"
            {...register("companyName")}
            className="w-full border border-input-border"
          />
          {errors.companyName && (
            <p className="text-sm text-red-500 text-start">
              {errors.companyName.message}
            </p>
          )}
        </div>

        <div>
          <Input
            type="text"
            placeholder="category"
            {...register("category")}
            className="w-full border border-input-border"
          />
          {errors.category && (
            <p className="text-sm text-red-500 text-start">
              {errors.category.message}
            </p>
          )}
        </div>
        <div>
          <Input
            type="number"
            inputMode="numeric"
            placeholder="Phone number"
            {...register("phone")}
            className="w-full border border-input-border"
          />
          {errors.phone && (
            <p className="text-sm text-red-500 text-start">
              {errors.phone.message}
            </p>
          )}
        </div>
        <div>
          <Input
            placeholder="Tax ID"
            {...register("taxId")}
            className="w-full border border-input-border"
          />
          {errors.taxId && (
            <p className="text-sm text-red-500 text-start">
              {errors.taxId.message}
            </p>
          )}
        </div>
        <div>
          {/* video form */}
          <label
            htmlFor="video-upload"
            className="flex items-center overflow-hidden border rounded-md cursor-pointer border-input-border"
          >
            <div className="flex-grow text-base">
              <p className="mx-3 text-gray-500 truncate text-start">
                {selectedFileName}
              </p>
            </div>
            <div className="flex items-center justify-center p-2">
              <img src={FileUpload} className="w-5 h-5 text-gray-500" />
            </div>
            <Input
              id="video-upload"
              type="file"
              accept="video/mp4,video/webm,video/ogg"
              {...register("video", {
                onChange: handleVideoChange,
              })}
              className="hidden"
            />
          </label>
          {errors.video && (
            <p className="text-sm text-red-500 text-start">
              {errors.video.message}
            </p>
          )}
        </div>
        <div>
          <Textarea
            placeholder="Refund Policy"
            {...register("refundPolicy")}
            className="w-full border border-input-border"
          />
          {errors.refundPolicy && (
            <p className="text-sm text-red-500 text-start">
              {errors.refundPolicy.message}
            </p>
          )}
        </div>
        <div>
          <Button type="submit" className="w-full mt-4" disabled={isPending}>
            {isPending ? "Submitting..." : "Next"}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default ProfileSetupForm;
