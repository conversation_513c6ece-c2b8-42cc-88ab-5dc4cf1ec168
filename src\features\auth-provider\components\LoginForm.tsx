import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import GoogleButton from "@/components/ui/google-button";
import { useState } from "react";
import FingerPrintImage from "@/assets/fingerprint.png";
import { Link, useNavigate } from "react-router-dom";
import { LuEye } from "react-icons/lu";
import { PiEyeSlash } from "react-icons/pi";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

import { LoginPayload, LoginResponse } from "../type";
import { LoginSchema } from "../validation";
import { loginApi } from "../api";
import { useAuthStore } from "@/store/authStore";
import { showToast } from "@/lib/toast";

function LoginForm() {
  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);
  const [showPassword, setShowPassword] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginPayload>({
    resolver: joiResolver(LoginSchema),
  });

  const { mutate, isPending } = useMutation<
    LoginResponse,
    AxiosError,
    LoginPayload
  >({
    mutationFn: loginApi,
    onSuccess: (response) => {
      console.log("Login success:", response);

      // Extract access token from the response
      let accessToken = "";
      if (response.accessToken) {
        accessToken = response.accessToken;
      } else if (response.data?.accessToken) {
        accessToken = response.data.accessToken;
      }
      // console.log(accessToken, "access token");
      // if (!accessToken) {
      //   showToast("No access token in response", "error");
      //   return;
      // }

      // Store the access token and role in the auth store
      // login({ accessToken, role: 'provider' });

      // Handle different response scenarios based on the API response

      // Case 1: Auth response - No profile setup completed
      if (response.data?.auth && !response.data.auth.hasCompletedProfile) {
        console.log("Provider has not completed any profile steps");
        // Store the auth ID for profile setup
        localStorage.setItem("userAuthId", response.data.auth._id);
        // Navigate to the first step of profile setup
        navigate("/provider/setup-profile");
        return;
      }

      // Case 2: Provider response - Some profile steps completed
      if (response.data?.provider) {
        const provider = response.data.provider;
        const accessToken = response.data.accessToken;

        // Store the provider ID and completion status for future API calls
        localStorage.setItem("providerId", provider._id);
        localStorage.setItem(
          "hasAddedServices",
          provider.hasAddedServices.toString()
        );
        localStorage.setItem(
          "hasSetAvailability",
          provider.hasSetAvailability.toString()
        );
        localStorage.setItem("planChoosen", provider.planChoosen.toString());

        // Check if provider is approved and has access token - if so, go to dashboard
        if (accessToken && provider.isApproved) {
          console.log("Provider is approved and verified - redirecting to dashboard");
          login({ accessToken, role: "provider" });
          navigate("/provider/dashboard");
          return;
        }

        // Check which steps are completed and redirect accordingly
        if (!provider.hasAddedServices) {
          // Navigate to service creation step (Step 2)
          console.log("Provider needs to add services");
          // First, we need to navigate to the profile setup page which will show Step 2
          // The ProfileSetupSteps component will handle showing the correct step
          navigate("/provider/setup-profile");
          return;
        }

        if (!provider.hasSetAvailability) {
          // Navigate to availability step (Step 3)
          console.log("Provider needs to set availability");
          // First, we need to navigate to the profile setup page which will show Step 3
          navigate("/provider/setup-profile");
          return;
        }

        if (!provider.planChoosen) {
          // Navigate to plan selection step (Step 4)
          console.log("Provider needs to choose a plan");
          navigate("/provider/setup-profile/plan-selection");
          return;
        }

        // All steps completed but not yet approved, navigate to pending verification page
        console.log("Provider has completed all profile steps but pending approval");
        navigate("/provider/setup-profile/pending-verification");
        return;
      }

      // Default case: Navigate to dashboard if access token is present
      console.log("Default navigation to dashboard");
      if (accessToken) {
        login({ accessToken, role: "provider" });
        navigate("/provider/dashboard");
      } else {
        showToast("Login failed - no access token received", "error");
      }
    },
    onError: (error: AxiosError) => {
      console.error("Login error:", error);

      if (error.response?.status === 401) {
        showToast("Invalid email or password", "error");
      } else if (error.response?.status === 403) {
        showToast("Your account is pending verification", "error");
      } else {
        showToast("Something went wrong", "error");
      }
    },
  });

  const onSubmit = (data: LoginPayload) => {
    mutate(data);
  };

  return (
    <div className="w-full max-w-md space-y-6 text-center">
      <div className="space-y-2">
        <Link to={"/"}>
          <img
            src={FingerPrintImage}
            alt="Fingerprint"
            className="object-cover mx-auto"
          />
        </Link>
        <h1 className="text-3xl font-bold md:text-4xl font-prettywise">
          Welcome Back
        </h1>
      </div>

      <GoogleButton text="Sign in with Google" />

      <div className="relative">
        <div className="absolute inset-0 flex items-center">
          <div className="w-full border-t" />
        </div>
        <div className="relative flex justify-center text-xs uppercase">
          <span className="px-2 bg-white text-muted-foreground">or</span>
        </div>
      </div>
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-4 text-left">
          {/* Email Input */}
          <div>
            <Input
              className="border-input-border focus:border-slate-300"
              placeholder="Email"
              type="email"
              {...register("email")}
            />
            {errors.email && (
              <p className="text-sm text-red-500">{errors.email.message}</p>
            )}
          </div>

          {/* Password Input */}
          <div>
            <div className="flex border rounded-sm border-input-border focus:border-slate-300">
              <Input
                className="border-none"
                placeholder="Password"
                type={showPassword ? "text" : "password"}
                {...register("password")}
              />
              {showPassword ? (
                <PiEyeSlash
                  onClick={() => setShowPassword(false)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              ) : (
                <LuEye
                  onClick={() => setShowPassword(true)}
                  className="text-[#827C7A] my-auto mr-2 hover:cursor-pointer"
                  size={25}
                />
              )}
            </div>
            {errors.password && (
              <p className="text-sm text-red-500">{errors.password.message}</p>
            )}
          </div>
        </div>

        <div className="flex justify-end my-4">
          <Link
            to="/provider/forgot-password"
            className="underline text-muted-foreground"
          >
            Forgot password?
          </Link>
        </div>

        <div className="flex flex-col mt-6 space-y-2 md:mt-12 gap-y-3 md:gap-y-1">
          <Button
            type="submit"
            className="w-full p-2 hover:bg-[#c65a3c]"
            disabled={isPending}
          >
            {isPending ? "Logging in..." : "Login"}
          </Button>
        </div>
      </form>
      <div className="flex justify-center gap-x-1">
        <p>Don't have an account?</p>
        <Link
          to="/provider/register"
          className="font-semibold underline text-orange-1"
        >
          Signup
        </Link>
      </div>
    </div>
  );
}

export default LoginForm;
