import React from "react";
import SignupImage from "@/assets/vecteezy_a-mother-love-embracing-her-newborn-baby-girl-pure_25078664 1.png";
import MobileSignupImage from "@/assets/signup-image.png";
function AuthLayout({ children }: { children: React.ReactNode }) {
  return (
    <>
      <div className="flex flex-col mb-2 md:mb-0 md:flex-row">
        <div className="md:w-1/2 mb-14 md:mb-0">
          <img
            src={SignupImage} 
            alt="Mom and baby"
            className="hidden md:block md:h-screen "
          />
          <img
            src={MobileSignupImage} 
            alt="Mom and baby"
            className="w-full md:hidden"
          />
        </div>
        <div className="w-full md:w-1/2 flex items-center justify-center">
          <div className="w-11/12 mx-auto">{children}</div>
        </div>
      </div>
    </>
  );
}

export default AuthLayout;
