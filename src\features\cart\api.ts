import api from "@/lib/axios";
import { AddressFormData, UpdateCartApiPayload, CartItem } from "./type";

export const fetchCartItemsApi = async () => {
  const { data } = await api.get(`/api/v1/cart`);
  return data;
};
export const fetchCartItemsCountApi = async () => {
  const { data } = await api.get(`/api/v1/cart/count`);
  return data;
};

export const fetchShipmentAddressApi = async () => {
  const { data } = await api.get(`/api/v1/users/customer/shipping-address`);
  return data;
};
export const updateShipmentAddressApi = async (payload: AddressFormData) => {
  const { data } = await api.patch(`/api/v1/users/customer/`, {
    shippingAddress: payload,
  });
  return data;
};

export const applyCouponApi = async (coupon: string) => {
  const { data } = await api.patch(`/api/v1/coupon`, { coupon });
  return data;
};
export const removeCouponApi = async (coupon: string) => {
  const { data } = await api.delete(`/api/v1/coupon`, { data: { coupon } });
  return data;
};

export const updateCartApi = async (payload: UpdateCartApiPayload) => {
  const { data } = await api.post("/api/v1/cart", payload);
  return data;
};

export const checkoutApi = async (payload: {
  cart: CartItem[];
  total: number;
}) => {
  const { data } = await api.post("/api/v1/order/checkout-session", payload);
  return data;
};
