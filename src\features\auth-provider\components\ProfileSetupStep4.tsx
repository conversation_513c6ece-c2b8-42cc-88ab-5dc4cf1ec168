import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { ChevronRight, ChevronUp } from "lucide-react";
import { useMutation } from "@tanstack/react-query";
import { subscriptionCheckoutApi, SubscriptionCheckoutPayload, coreProviderSubscriptionApi, CoreProviderSubscriptionPayload } from "../api";
import { showToast } from "@/lib/toast";
import { useNavigate } from "react-router-dom";
// Note: We're using direct window.location.href redirection instead of Stripe.js
// This is because the API returns a complete checkout URL

// Plan type definition
type PlanId = "core" | "growth" | "premier";

type Plan = {
  id: PlanId;
  title: string;
  price: string;
  period: string;
  description: string;
  features: string[];
  readMoreText: string;
};

// Plan data
const plans: Plan[] = [
  {
    id: "core",
    title: "Core Provider (Free)",
    price: "$0",
    period: "/month",
    description:
     "Providers establishing their online presence and getting initial bookings.",
    features: [
      "Core Marketplace Listing: Name, credentials, location, and core services listed.",
      "Basic Search Visibility: Appears in relevant search results but below Growth and Premier listings.",
      "Client Appointment Booking: Clients can book directly via the marketplace.",
      "Client Reviews: Clients can leave reviews; providers cannot feature or pin them.",
      "Performance Insights: Access to basic marketplace analytics (e.g., bookings).",
      "Booking Fee: $35 per completed appointment. Basic profile visibility",
    ],
    readMoreText: "Read more...",
  },
  {
    id: "growth",
    title: "Growth Provider",
    price: "$50",
    period: "/month",
    description:
     "Providers looking to increase appointment bookings and market share.",
    features: [
      "Enhanced Marketplace Listing: Bio, service offerings, and introductory video to showcase expertise.",
      "Priority Search Visibility: Appears above Core providers in relevant search results.",
      "Highlighted Client Reviews: Ability to pin up to two testimonials for credibility.",
      "Targeted Social Media Spotlight: One quarterly post across Nurture's social media platforms.",
      "Growth Analytics Dashboard: Access to performance metrics, including conversion rates and client booking trends.",
      "Booking Fee: $30 per completed appointment. (Save 15% on booking fees compared to Core Plan)",
    ],
    readMoreText: "Read more...",
  },
  {
    id: "premier",
    title: "Premier Provider Plan",
    price: "$200",
    period: "/month",
    description:
     "Providers seeking top-tier marketing support and maximum client visibility.",
    features: [
      "Premier Marketplace Listing: Customizable profile with a \"Verified Premier Provider\" badge and expanded content options.",
      "Top Search Priority: Always listed at the top of search results within relevant categories.",
      "Unlimited Review Pinning: Showcase multiple positive client testimonials.",
      "Exclusive Social Media Features: Monthly posts on Nurture Postnatal Care's social media channels, including stories (content support provided).",
      "Dedicated Email Campaign: Individual feature in a \"Meet Our Premier Providers\" email sent to all marketplace members.",
      "Thought Leadership Opportunities: One guest blog post annually, promoted through Nurture's marketing channels.",
      "Advanced Performance Analytics: Access to client demographic data, appointment trends, and lead sources.",
      "Early Feature Access: Participate in beta testing and provide feedback on new marketplace features.",
      "Booking Fee: $25 per completed appointment. (Save 30% compared to Core plan)",
    ],
    readMoreText: "Read more...",
  },
];

// Comparison table data
const comparisonData = [
  {
    feature: "Marketplace Listing",
    core: "Core",
    growth: "Enhanced",
    premier: "Premium",
  },
  {
    feature: "Search Visibility",
    core: "Standard",
    growth: "Priority",
    premier: "Top",
  },
  {
    feature: "Analytics Dashboard",
    core: "Basic",
    growth: "Growth Analytics",
    premier: "Advanced Analytics",
  },
  {
    feature: "Client Reviews",
    core: "Standard",
    growth: "Pin up to 2",
    premier: "Unlimited",
  },
  {
    feature: "Social Media Features",
    core: "None",
    growth: "1 post/quarter",
    premier: "Monthly posts",
  },
  {
    feature: "Email Spotlight",
    core: "None",
    growth: "Quarterly",
    premier: "Monthly",
  },
  {
    feature: "Blog/Thought Leadership",
    core: "None",
    growth: "None",
    premier: "Annual feature",
  },

  {
    feature: "Blog/Thought Leadership",
    core: "Basic",
    growth: "Growth Dashboard",
    premier: "Advanced Dashboard",
  },

  {
    feature: "Beta Feature Access",
    core: "No",
    growth: "No",
    premier: "Yes",
  },
  {
    feature: "Booking Fee (Per Completed Appt)",
    core: "$35",
    growth: "$30",
    premier: "$25",
  },
];

type ProfileSetupStep4Props = {
  onBack: () => void;
};

export default function ProfileSetupStep4({
  onBack,
}: ProfileSetupStep4Props) {
  // Type-safe state for expanded cards
  const [expandedCards, setExpandedCards] = useState<Record<PlanId, boolean>>({
    core: false,
    growth: false,
    premier: false,
  });

  // State for selected plan and provider ID
  const [selectedPlan, setSelectedPlan] = useState<PlanId | null>(null);
  const [providerId, setProviderId] = useState<string>("");
  const navigate = useNavigate();

  // Get providerId from localStorage
  useEffect(() => {
    const storedProviderId = localStorage.getItem("providerId");
    if (storedProviderId) {
      console.log("Provider ID found in localStorage:", storedProviderId);
      setProviderId(storedProviderId);
    } else {
      console.error("Provider ID not found in localStorage");
      showToast("Provider ID not found. Please go back and complete the previous steps first.", "error");
    }
  }, []);

  // Subscription checkout mutation for paid plans
  const { mutate: createCheckout, isPending: isCheckoutPending } = useMutation({
    mutationFn: subscriptionCheckoutApi,
    onSuccess: async (response) => {
      if (response.status === "OK" && response.data?.url) {
        // Update the status flag for plan selection before redirecting
        localStorage.setItem("planChoosen", "true");

        console.log("Redirecting to Stripe checkout URL:", response.data.url);
        // Redirect to Stripe checkout
        window.location.href = response.data.url;
      } else {
        console.error("Invalid checkout response:", response);
        showToast("Failed to create checkout session", "error");
      }
    },
    onError: (error) => {
      console.error("Checkout creation error:", error);
      showToast("Failed to create checkout session", "error");
    }
  });

  // Core Provider subscription mutation for free plan
  const { mutate: subscribeToCoreProvider, isPending: isCoreProviderPending } = useMutation({
    mutationFn: coreProviderSubscriptionApi,
    onSuccess: async (response) => {
      if (response.status === "OK" && response.data?.subscription) {
        // Update the status flag for plan selection
        localStorage.setItem("planChoosen", "true");

        console.log("Core Provider subscription successful:", response.data.subscription);
        showToast("Successfully subscribed to Core Provider plan", "success");

        // Redirect to pending verification page
        navigate("/provider/setup-profile/pending-verification");
      } else {
        console.error("Invalid core provider subscription response:", response);
        showToast("Failed to subscribe to Core Provider plan", "error");
      }
    },
    onError: (error) => {
      console.error("Core Provider subscription error:", error);
      showToast("Failed to subscribe to Core Provider plan", "error");
    }
  });

  // Combined loading state
  const isPending = isCheckoutPending || isCoreProviderPending;

  const toggleCardExpansion = (planId: PlanId) => {
    setExpandedCards({
      ...expandedCards,
      [planId]: !expandedCards[planId],
    });
  };

  // Map plan IDs to API plan names
  const planMapping: Record<PlanId, "coreProvider" | "growthProvider" | "premierProvider"> = {
    core: "coreProvider",
    growth: "growthProvider",
    premier: "premierProvider"
  };

  const handleSelectPlan = (planId: PlanId) => {
    setSelectedPlan(planId);

    if (!providerId) {
      showToast("Provider ID not found. Please go back and complete the previous steps first.", "error");
      return;
    }

    // If selecting the free plan, call the Core Provider subscription API
    if (planId === "core") {
      const payload: CoreProviderSubscriptionPayload = {
        providerId,
      };

      console.log("Creating Core Provider subscription with payload:", payload);
      subscribeToCoreProvider(payload);
      return;
    }

    // For paid plans, create a checkout session
    const payload: SubscriptionCheckoutPayload = {
      plan: planMapping[planId],
      providerId,
    };

    console.log("Creating checkout session with payload:", payload);
    createCheckout(payload);
  };

  return (
    <div className="w-full provider-plan-selection-step">
      {/* Back button */}

      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4 relative">
          <button
            onClick={onBack}
            disabled={isPending}
            className={`${isPending ? 'text-gray-400 cursor-not-allowed' : 'text-gray-600 hover:text-gray-800'} absolute left-0`}
            aria-label="Go back"
          >
            ←
          </button>
          <h1 className="text-3xl font-prettywise font-bold">
            Select a plan
          </h1>
        </div>
        <p className="text-gray-600 max-w-[700px] mb-[64px] mx-auto">
          Nurture Postnatal Care's provider marketplace offers three membership
          tiers — Core, Growth, and Premier — with a simplified, commission-free structure. Providers pay a booking fee per
          completed appointment, with discounts available at higher membership tiers.
        </p>
      </div>

      {/* Plan Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
        {plans.map((plan) => (
          <Card
            key={plan.id}
            className="border border-gray-200 shadow-sm overflow-hidden"
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-lg text-center">
                {plan.title}
              </CardTitle>
              <div className="flex items-end justify-center mt-2 mb-3">

                <span className="text-2xl font-bold">{plan.price}</span>

                <span className=" ml-1">{plan.period}</span>
              </div>
              <Button
                onClick={() => handleSelectPlan(plan.id)}
                variant="outline"
                disabled={isPending || !providerId}
                className="w-full border-orange-1 text-orange-1 hover:bg-orange-1 hover:text-white rounded-full !mt-3"
              >
                {isPending && selectedPlan === plan.id ? "Processing..." : "Select Plan"}
              </Button>
            </CardHeader>
            <CardContent className="pb-4 mt-6">
              <CardDescription className="mb-6 font-medium text-black text-left">
              <div className=" text-left text-black mb-4 ">Ideal for:</div>
                {plan.description}
              </CardDescription>
              <div className="text-sm text-left font-medium mb-2">
                What's Included
              </div>
              <div className="space-y-3">
                {plan.features
                  .slice(0, expandedCards[plan.id] ? plan.features.length : 2)
                  .map((feature, index) => (
                    <div className="flex text items-start">
                      <span className="mt-[-3px]">•</span>
                      <p  key={index} className="text-sm ml-2 text-left">{feature}</p>
                    </div>
                  ))}
                {!expandedCards[plan.id] && plan.features.length > 2 && (
                  <button
                    onClick={() => toggleCardExpansion(plan.id)}
                    className="text-orange-1 text-sm flex items-center mt-2"
                  >
                    {plan.readMoreText}{" "}
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </button>
                )}
                {expandedCards[plan.id] && (
                  <button
                    onClick={() => toggleCardExpansion(plan.id)}
                    className="text-orange-1 text-sm flex items-center mt-2"
                  >
                    Show less{" "}
                    <ChevronUp className="h-4 w-4 ml-1" />
                  </button>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Membership Tier Comparison */}
      <div className="mb-8">
        <h3 className="text-3xl font-prettywise font-bold text-center mb-6">
          Membership Tier Comparison
        </h3>
        <div className="overflow-x-auto">
          <div className=" overflow-hidden">
            <Table className="border-collapse">
            <TableHeader>
              <TableRow className="bg-gray-50 h-[80px] text-lg font-semibold ">
                <TableHead className="w-[200px] border text-left font-semibold">Feature</TableHead>
                <TableHead className="border  font-semibold text-left">
                  Core Provider
                  <br />
                  ($0/mo)
                </TableHead>
                <TableHead className="border font-semibold text-left">
                  Growth Provider
                  <br />
                  ($50/mo)
                </TableHead>
                <TableHead className="border font-semibold text-left">
                  Premier Provider
                  <br />
                  ($200/mo)
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="text-left">
              {comparisonData.map((row, index) => (
                <TableRow
                  key={index}
                  className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                >
                  <TableCell
                    className={`font-medium border ${index === comparisonData.length - 1 ? "rounded-bl-lg" : ""}`}
                  >
                    {row.feature}
                  </TableCell>
                  <TableCell className="border text-left">
                    {row.core}
                  </TableCell>
                  <TableCell className="border text-left">
                    {row.growth}
                  </TableCell>
                  <TableCell
                    className={`border text-left`}
                  >
                    {row.premier}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          </div>
        </div>
      </div>
    </div>
  );
}
