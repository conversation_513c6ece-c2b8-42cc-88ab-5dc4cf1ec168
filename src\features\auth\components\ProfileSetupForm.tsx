import FingerPrintImage from "@/assets/fingerprint.png";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useForm } from "react-hook-form";
import { joiResolver } from "@hookform/resolvers/joi";
import { useMutation } from "@tanstack/react-query";
import { setupProfileSchema } from "../validation";
import { setupProfileApi } from "../api";
import { profileData } from "../type";
import { useNavigate } from "react-router-dom";
import { useAuthStore } from "@/store/authStore";
import { showToast } from "@/lib/toast";

function ProfileSetupForm() {
  const navigate = useNavigate();
  const login = useAuthStore((state) => state.login);
  const {
    register,
    handleSubmit,
    setValue,

    formState: { errors },
  } = useForm<profileData>({
    resolver: joiResolver(setupProfileSchema),
  });
  const { mutate, isPending } = useMutation({
    mutationFn: setupProfileApi,
    onSuccess: ({ data }) => {
      login({ accessToken: data.accessToken, role: "customer" });
      navigate("/questionaire");
    },
    onError: () => {
      showToast("Something went wrong", "error");
    },
  });

  const onSubmit = (data: profileData) => {
    const authId = localStorage.getItem("userAuthId") as string;
    // localStorage.removeItem("userAuthId");
    mutate({ ...data, authId });
  };

  return (
    <div className="flex flex-col items-center justify-center w-full max-w-md mx-auto md:mx-0 px-4 text-center space-y-5">
      <img
        src={FingerPrintImage}
        alt="Fingerprint"
        className="mx-auto object-cover w-16 h-16"
      />
      <h1 className="text-2xl md:text-3xl font-prettywise font-bold">
        Setup your profile
      </h1>
      <form className="w-full space-y-4" onSubmit={handleSubmit(onSubmit)}>
        <div className="flex gap-4">
          <div className="w-full">
            <Input
              type="text"
              placeholder="First name"
              {...register("firstName")}
              className="w-full border border-input-border"
            />
            {errors.firstName && (
              <p className="text-red-500 text-sm">{errors.firstName.message}</p>
            )}
          </div>
          <div className="w-full">
            <Input
              type="text"
              placeholder="Last name"
              {...register("lastName")}
              className="w-full border border-input-border"
            />
            {errors.lastName && (
              <p className="text-red-500 text-sm">{errors.lastName.message}</p>
            )}
          </div>
        </div>
        <div>
          <Input
            type="number"
            inputMode="numeric"
            placeholder="Phone number"
            {...register("phone")}
            className="w-full border border-input-border"
          />
          {errors.phone && (
            <p className="text-red-500 text-start text-sm">
              {errors.phone.message}
            </p>
          )}
        </div>
        <div>
          <Input
            type="text"
            placeholder="Address"
            {...register("address")}
            className="w-full border border-input-border"
          />
          {errors.address && (
            <p className="text-red-500 text-start text-sm">
              {errors.address.message}
            </p>
          )}
        </div>
        <div>
          <Select
            onValueChange={(
              value: "currently pregnant" | "loss history" | "postpartum"
            ) => setValue("birthStatus", value)}
          >
            <SelectTrigger className="w-full border-input-border">
              <SelectValue placeholder="Birth Status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="currently pregnant">
                Currently pregnant
              </SelectItem>
              <SelectItem value="postpartum">Postpartum</SelectItem>
              <SelectItem value="loss history">Loss history</SelectItem>
            </SelectContent>
          </Select>
          {errors.birthStatus && (
            <p className="text-red-500 text-start text-sm">
              {errors.birthStatus.message}
            </p>
          )}
        </div>
        <div>
          <Select
            onValueChange={(value) => setValue("preferredLanguage", value)}
          >
            <SelectTrigger className="w-full border-input-border">
              <SelectValue placeholder="Preferred Language" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="english">English</SelectItem>
            </SelectContent>
          </Select>
          {errors.preferredLanguage && (
            <p className="text-red-500 text-start text-sm">
              {errors.preferredLanguage.message}
            </p>
          )}
        </div>
        <div>
          <Button type="submit" className="w-full mt-6" disabled={isPending}>
            {isPending ? "Submitting..." : "Continue"}
          </Button>
        </div>
      </form>
    </div>
  );
}

export default ProfileSetupForm;
