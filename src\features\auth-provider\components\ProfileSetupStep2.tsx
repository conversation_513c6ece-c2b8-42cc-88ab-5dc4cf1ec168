import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Trash2 } from "lucide-react";
import ServiceDetailsDialog, { ServiceDetails } from "./ServiceDetailsDialog";
import { useMutation } from "@tanstack/react-query";
import { createServiceApi, ServiceCreationPayload } from "../api";
import { showToast } from "@/lib/toast";

type ProfileSetupStep2Props = {
  onNext: () => void;
  // onBack: () => void;
};

export default function ProfileSetupStep2({ onNext }: ProfileSetupStep2Props) {
  const [dialogOpen, setDialogOpen] = useState(false);
  const [services, setServices] = useState<ServiceDetails[]>([]);
  const [providerId, setProviderId] = useState<string>("");
  const [isCreatingServices, setIsCreatingServices] = useState(false);

  // Get providerId from localStorage or API response
  useEffect(() => {
    const storedProviderId = localStorage.getItem("providerId");
    if (storedProviderId) {
      console.log("Provider ID found in localStorage:", storedProviderId);
      setProviderId(storedProviderId);
    } else {
      console.error("Provider ID not found in localStorage");
      showToast("Provider ID not found. Please go back and complete the basic details step first.", "error");
    }
  }, []);

  // Service creation mutation
  const { mutate: createService } = useMutation({
    mutationFn: createServiceApi,
    onSuccess: (response) => {
      if (response.status === "OK" && response.data?.services) {
        console.log("Service created successfully:", response.data.services._id);
      }
    },
    onError: (error) => {
      console.error("Service creation error:", error);
      throw error; // Re-throw to handle in the calling function
    }
  });

  const handleAddService = (serviceData: ServiceDetails) => {
    // Only add to local state for UI display - no API call yet
    setServices([...services, serviceData]);
    showToast("Service added locally", "success");
  };

  const handleRemoveService = (index: number) => {
    const updatedServices = [...services];
    updatedServices.splice(index, 1);
    setServices(updatedServices);
    showToast("Service removed", "success");
  };

  // Function to create all services via API when Next is clicked
  const handleCreateAllServices = async () => {
    if (!providerId) {
      showToast("Provider ID not found. Please go back and complete the basic details step first.", "error");
      return;
    }

    if (services.length === 0) {
      showToast("Please add at least one service", "error");
      return;
    }

    setIsCreatingServices(true);

    try {
      // Create all services sequentially
      for (const serviceData of services) {
        const servicePayload: ServiceCreationPayload = {
          title: serviceData.serviceName,
          providerId: providerId,
          duration: parseInt(serviceData.duration),
          description: serviceData.description,
          price: parseFloat(serviceData.price),
          highlights: serviceData.highlights.map(h => h.text)
        };

        console.log("Creating service:", servicePayload);

        // Create service via API (this will be awaited due to mutation)
        await new Promise((resolve, reject) => {
          createService(servicePayload, {
            onSuccess: () => resolve(true),
            onError: (error) => reject(error)
          });
        });
      }

      // All services created successfully
      localStorage.setItem("hasAddedServices", "true");
      showToast("All services created successfully", "success");
      onNext();
    } catch (error) {
      console.error("Error creating services:", error);
      showToast("Failed to create some services. Please try again.", "error");
    } finally {
      setIsCreatingServices(false);
    }
  };

  return (
    <div className="w-full">
      {/* Add Service Button */}
      <Button
        onClick={() => setDialogOpen(true)}
        variant="outline"
        className="w-full flex items-center justify-center gap-2 mb-6"
      >
        Add Service <Plus className="h-4 w-4" />
      </Button>

      {/* Service Details Dialog */}
      <ServiceDetailsDialog
        open={dialogOpen}
        onOpenChange={setDialogOpen}
        onSubmit={handleAddService}
      />

      {/* Services List */}
      <div className="space-y-4 mb-8">
        {services.map((service, index) => (
          <div
            key={index}
            className="border border-gray-200 rounded-md p-4 relative"
          >
            <div className="flex justify-between items-start">
              <div>
                <h3 className="font-medium text-lg">{service.serviceName}</h3>
                <p className="text-base text-left text-gray-500">{service.duration}</p>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium text-lg">${service.price}</span>
                <Button
                  onClick={() => handleRemoveService(index)}
                  variant="ghost"
                  size="icon"
                  className="h-8 w-8 text-red-500"
                >
                  <Trash2 className="h-6 w-6" />
                </Button>
              </div>
            </div>

            {service.highlights.length > 0 && (
              <div className="mt-2">
                <ul className="list-disc list-inside text-left text-base text-gray-600">
                  {service.highlights.map((highlight, idx) => (
                    <li key={idx}>{highlight.text}</li>
                  ))}
                </ul>
              </div>
            )}

            <p className="mt-2 text-base text-left text-gray-600">{service.description}</p>


          </div>
        ))}
      </div>

      {/* Navigation Buttons */}
      <div className="flex justify-between mt-6">
        {/* <Button
          onClick={onBack}
          variant="ghost"
          className="text-orange-1 w-full rounded-full"
          disabled={isCreatingServices}
        >
          Back
        </Button> */}
        <Button
          onClick={handleCreateAllServices}
          disabled={services.length === 0 || isCreatingServices || !providerId}
          className="w-full"
        >
          {isCreatingServices ? "Creating Services..." : "Next"}
        </Button>
      </div>
    </div>
  );
}
