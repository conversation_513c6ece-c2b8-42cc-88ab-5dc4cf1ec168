import { Button } from "@/components/ui/button";
import GoogleIcon from "@/assets/google-icon.png";

type GoogleButtonProps = {
  text: string;
};
export default function GoogleButton({ text }: GoogleButtonProps) {
  return (
    <Button
      variant="outline"
      className="flex items-center justify-center w-full gap-2 p-3 text-black rounded-sm"
    >
      <div className="flex gap-x-2">
        <img src={GoogleIcon} alt="Google Icon" className="my-auto w-7 h-7" />
        <p className="text-xl">{text}</p>
      </div>
    </Button>
  );
}
