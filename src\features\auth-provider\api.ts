import api from "@/lib/axios";
import { RegisterPayload, LoginPayload, OtpPayloadData, forgotPasswordPayload, resetPasswordPayload, LoginResponse } from "./type";

// Using the same API endpoints as customer and seller authentication
export const registerApi = async (payload: RegisterPayload) => {
  try {
    // Send the payload without adding a role field
    // The API will determine the role based on the endpoint or other mechanisms
    const response = await api.post("/api/v1/auth/signup", payload);

    // Normalize the response to handle different formats
    const data = response.data;

    // Return the normalized response
    return data;
  } catch (error) {
    console.error("Register API error:", error);
    throw error;
  }
};

export const loginApi = async (payload: LoginPayload): Promise<LoginResponse> => {
  try {
    // Use the provider-specific login endpoint
    const response = await api.post("/api/v1/auth/provider/login", payload);
    console.log("Provider login API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Provider login API error:", error);
    throw error;
  }
};

export const verifyOtpApi = async (payload: OtpPayloadData) => {
  try {
    const response = await api.post("/api/v1/auth/verify-otp", payload);
    console.log("Verify OTP API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Verify OTP API error:", error);
    throw error;
  }
};

export const resendOtpApi = async (payload: { email: string }) => {
  try {
    const response = await api.post("/api/v1/auth/resend-otp", payload);
    console.log("Resend OTP API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Resend OTP API error:", error);
    throw error;
  }
};

export const forgotPasswordApi = async (payload: forgotPasswordPayload) => {
  const { data } = await api.post("/api/v1/auth/forgot-password", payload);
  return data;
};

export const validateResetPasswordTokenApi = async (payload: string) => {
  const { data } = await api.get(`/api/v1/auth/validate-reset-token`, {
    params: {
      token: payload,
    },
  });
  return data;
};

export const resetPasswordApi = async (payload: resetPasswordPayload) => {
  const { data } = await api.post("/api/v1/auth/reset-password", payload);
  return data;
};

export const setupProfileApi = async (payload: FormData) => {
  try {
    const response = await api.post("/api/v1/users/provider", payload, {
      headers: {
        "Content-Type": "multipart/form-data",
      },
    });
    return response.data;
  } catch (error) {
    console.error("Setup profile API error:", error);
    throw error;
  }
};

export interface ServiceCreationPayload {
  title: string;
  providerId: string;
  duration: number;
  description: string;
  price: number;
  highlights?: string[];
}

export const createServiceApi = async (payload: ServiceCreationPayload) => {
  try {
    console.log("Creating service with API:", payload);
    console.log("Provider ID in payload:", payload.providerId);

    const response = await api.post("/api/v1/service", payload);
    console.log("Service creation API response:", response.data);
    return response.data;
  } catch (error) {
    console.error("Service creation API error:", error);
    throw error;
  }
};

export interface TimeSlotData {
  start: string;
  end: string;
}

export interface AvailabilityPayload {
  providerId: string;
  timeSlot: {
    monday?: TimeSlotData[];
    tuesday?: TimeSlotData[];
    wednesday?: TimeSlotData[];
    thursday?: TimeSlotData[];
    friday?: TimeSlotData[];
    saturday?: TimeSlotData[];
    sunday?: TimeSlotData[];
  };
}

export const setProviderAvailabilityApi = async (payload: AvailabilityPayload) => {
  try {
    console.log("Setting provider availability with API:", payload);

    // Log the time format for debugging
    Object.entries(payload.timeSlot).forEach(([day, slots]) => {
      if (slots.length > 0) {
        console.log(`${day} time slots:`, slots);
        slots.forEach((slot, index) => {
          console.log(`  Slot ${index + 1}: start=${slot.start}, end=${slot.end}`);
        });
      }
    });

    const response = await api.post("/api/v1/users/provider/availability", payload);
    console.log("Availability API response:", response.data);
    return response.data;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error("Availability API error:", error);
    if (error.response?.data) {
      console.error("API error details:", error.response.data);
    }
    throw error;
  }
};

export interface SubscriptionCheckoutPayload {
  plan: "coreProvider" | "growthProvider" | "premierProvider";
  providerId: string;
  cancelUrl?: string;
  successUrl?: string;
}

export const subscriptionCheckoutApi = async (payload: SubscriptionCheckoutPayload) => {
  try {
    console.log("Creating subscription checkout with API:", payload);

    const response = await api.post("/api/v1/subscription/checkout", payload);
    console.log("Subscription checkout API response:", response.data);

    return response.data;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error("Subscription checkout API error:", error);
    if (error.response?.data) {
      console.error("API error details:", error.response.data);
    }
    throw error;
  }
};

export interface CoreProviderSubscriptionPayload {
  providerId: string;
}

export const coreProviderSubscriptionApi = async (payload: CoreProviderSubscriptionPayload) => {
  try {
    console.log("Creating core provider subscription with API:", payload);

    const response = await api.post("/api/v1/users/provider/subscription/core-provider", payload);
    console.log("Core provider subscription API response:", response.data);
    return response.data;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    console.error("Core provider subscription API error:", error);
    if (error.response?.data) {
      console.error("API error details:", error.response.data);
    }
    throw error;
  }
};
