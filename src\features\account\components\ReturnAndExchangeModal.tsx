import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Modal } from "@/components/ui/modal";
import { Textarea } from "@/components/ui/textarea";
import { useMutation } from "@tanstack/react-query";
import { useState } from "react";
import { returnOrExchangeApi } from "../api";
import { useParams } from "react-router-dom";
import { showToast } from "@/lib/toast";
import { useOrderStore } from "@/store/orderStore";
function ReturnAndExchangeModal({
  type,
  isOpen,
  onClose,
}: {
  type: "return" | "exchange";
  isOpen: boolean;
  onClose: () => void;
}) {
  const checkBoxOptions = [
    "Damaged or Defective Item",
    "Incorrect Size or Fit",
    "Received the wrong product",
    "Product Doesn't Match Expectations",
  ];
  const { id } = useParams<{ id: string }>();
  const { fetchOrders } = useOrderStore();

  const [openTextArea, setOpenTextArea] = useState(false);
  const [textAreaValue, setTextAreaValue] = useState("");
  const [error, setError] = useState("");
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);
  const [imageError, setImageError] = useState<string>("");

  const removeImage = (index: number) => {
    const updatedImages = [...selectedImages];
    const updatedPreviews = [...imagePreviewUrls];
    updatedImages.splice(index, 1);
    updatedPreviews.splice(index, 1);
    setSelectedImages(updatedImages);
    setImagePreviewUrls(updatedPreviews);
  };
  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      if (selectedImages.length >= 5) {
        setImageError("You can only upload up to 5 images");
        return;
      }
      const newFile = e.target.files[0];

      // Validate file type
      const validTypes = ["image/jpeg", "image/png", "image/jpg", "image/webp"];
      if (!validTypes.includes(newFile.type)) {
        setImageError("Please upload a valid image file (JPEG, PNG, WebP)");
        return;
      }

      // Validate file size (max 5MB)
      const maxSize = 5 * 1024 * 1024; // 5MB
      if (newFile.size > maxSize) {
        setImageError("Image size should be less than 5MB");
        return;
      }

      setImageError("");
      setSelectedImages([...selectedImages, newFile]);

      // Create preview URL
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreviewUrls([...imagePreviewUrls, reader.result as string]);
      };
      reader.readAsDataURL(newFile);
    }
  };
  const handleTextAreaChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setTextAreaValue(e.target.value);
  };

  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const handleCheckboxChange = (option: string) => {
    if (selectedOptions.includes(option)) {
      setSelectedOptions(selectedOptions.filter((item) => item !== option));
    } else {
      setSelectedOptions([...selectedOptions, option]);
    }
  };
  const onToggleTextArea = () => setOpenTextArea(!openTextArea);

  const handleClose = (e: React.MouseEvent<HTMLButtonElement, MouseEvent>) => {
    e.stopPropagation();
    onClose();
  };

  const resetValues = () => {
    setTextAreaValue("");
    setSelectedOptions([]);
    setSelectedImages([]);
    setImagePreviewUrls([]);
    setError("");
    setImageError("");
  };
  // api call
  const { mutate, isPending: loading } = useMutation({
    mutationFn: returnOrExchangeApi,
    onSuccess: () => {
      showToast(`Request sent for ${type} `, "success");
      fetchOrders();
      onClose();
      resetValues();
    },
    onError: (error) => {
      showToast(`failed to request ${type} `, "error");
      console.log(error);
    },
  });

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (textAreaValue && textAreaValue.length < 6) {
      setError("Minimum 6 characters required for reason");
      return;
    }
    if (textAreaValue && textAreaValue.length > 200) {
      setError("Maximum character limit reached for reason");
      return;
    }
    if (selectedOptions.length === 0 && !openTextArea) {
      setError("Please select at least one reason");
      return;
    }
    if (selectedImages.length === 0) {
      setImageError("Please upload at least one product image");
      return;
    }
    setImageError("");
    setError("");

    mutate({
      orderId: id as string,
      type,
      reason: selectedOptions,
      images: selectedImages,
    });
  };

  return (
    <Modal isOpen={isOpen} onClose={onClose}>
      <form onSubmit={(e: React.FormEvent<HTMLFormElement>) => handleSubmit(e)}>
        <div className="container max-h-[480px] scrollbar-hide overflow-y-scroll">
          <h1 className="text-lg font-bold">
            Do you want to {type} this order?
          </h1>
          <h4 className="mt-3 text-neutral-300">
            Please give the reason why you’re
            {type === "return" ? " returning" : " exchanging"} this item.
          </h4>
          <div className="mt-10">
            {error && <p className="text-sm text-red-500">{error}</p>}
            {checkBoxOptions.map((option) => (
              <div key={option} className="flex items-center my-6 space-x-2">
                <Checkbox
                  onClick={() => handleCheckboxChange(option)}
                  checked={selectedOptions.includes(option)}
                  id={option}
                  className="data-[state=checked]:bg-orange-1 data-[state=checked]:text-white data-[state=checked]:border-0"
                />
                <Label
                  htmlFor={option}
                  className="leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {option}
                </Label>
              </div>
            ))}
            <div className="flex items-center my-6 space-x-2">
              <Checkbox
                onClick={onToggleTextArea}
                id={"other"}
                className="data-[state=checked]:bg-orange-1 data-[state=checked]:text-white data-[state=checked]:border-0"
              />
              <Label
                htmlFor={"other"}
                className="leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
              >
                Other
              </Label>
            </div>
            {openTextArea && (
              <Textarea
                value={textAreaValue}
                onChange={handleTextAreaChange}
                className="w-full h-24 p-4 border rounded-lg"
                placeholder="Please specify the reason"
              />
            )}
          </div>
          {/* image upload section */}
          <div className="container mt-10">
            <h1 className="font-bold">Upload images</h1>
            <h4 className="mt-3 text-neutral-300">
              Please provide the images of product that you’re trying to {type}.
            </h4>
          </div>
          {imageError && (
            <p className="mt-5 text-sm text-red-500">{imageError}</p>
          )}
          <div className="flex gap-2 mt-2 overflow-x-auto">
            {/* Image upload button */}
            <label htmlFor="image-upload" className="cursor-pointer">
              <div className="w-[80px] h-[80px] border border-dashed border-red-200 rounded flex items-center justify-center bg-red-50">
                <span className="text-2xl text-red-400">+</span>
              </div>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                onChange={handleImageChange}
                className="hidden"
              />
            </label>

            {/* Image previews */}
            {imagePreviewUrls.map((url, index) => (
              <div
                key={index}
                className="relative w-[80px] h-[80px] border rounded"
              >
                <img
                  src={url}
                  alt={`Product ${index}`}
                  className="object-cover w-full h-full"
                />
                <button
                  type="button"
                  onClick={() => removeImage(index)}
                  className="absolute flex items-center justify-center w-5 h-5 bg-white rounded-full shadow-sm -top-2 -right-2"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
        </div>
        {/* footer */}
        <div className="flex justify-end mt-3 gap-x-4">
          <Button
            disabled={loading}
            variant={"outline"}
            className="px-10"
            onClick={(e: React.MouseEvent<HTMLButtonElement, MouseEvent>) =>
              handleClose(e)
            }
          >
            No
          </Button>
          <Button className="px-12" disabled={loading}>
            {loading
              ? "Submitting..."
              : type === "return"
              ? "Return"
              : "Send  for exchange"}
          </Button>
        </div>
      </form>
    </Modal>
  );
}

export default ReturnAndExchangeModal;
