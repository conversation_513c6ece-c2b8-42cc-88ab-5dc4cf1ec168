export type RegisterPayload = {
  email: string | undefined;
  password: string | undefined;
};
export type LoginPayload = {
  email: string;
  password: string;
};

export type GoogleButtonProps = {
  text: string;
};

export type FormData = {
  email: string;
  password: string;
  confirmPassword: string;
};

export type OtpPayloadData = {
  otp: string;
  email: string;
};

export type profileData = {
  authId?: string;
  companyName: string;
  category: string;
  taxId: string;
  phone: string;
  refundPolicy: string;
  video: FileList;
};

export type forgotPasswordPayload = {
  email: string;
};
export type resetPasswordPayload = {
  newPassword: string;
  confirmPassword: string;
  token: string;
};

export type ProductCreationPayload = {
  _id?: string;
  title: string;
  description: string;
  sellerId: string;
  quantity: number;
  price: number;
  images?: { image: string; key: string }[];
};

export type ProductCardProps = {
  title: string;
  price: string;
  image?: FileList;
};
