import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Plus, Trash2 } from "lucide-react";
import { TimePicker } from "@/components/ui/time-picker";

type TimeSlot = {
  id: string;
  startTime: string;
  endTime: string;
};

type DaySchedule = {
  day: string;
  isAvailable: boolean;
  timeSlots: TimeSlot[];
};

export default function Availability() {
  // Initialize schedule for all days of the week
  const [schedule, setSchedule] = useState<DaySchedule[]>([
    { day: "SUN", isAvailable: false, timeSlots: [] },
    { day: "MON", isAvailable: true, timeSlots: [{ id: crypto.randomUUID(), startTime: "09:00 am", endTime: "05:00 pm" }] },
    { day: "TUE", isAvailable: true, timeSlots: [{ id: crypto.randomUUID(), startTime: "09:00 am", endTime: "05:00 pm" }] },
    { day: "WED", isAvailable: true, timeSlots: [{ id: crypto.randomUUID(), startTime: "09:00 am", endTime: "05:00 pm" }] },
    { day: "THU", isAvailable: true, timeSlots: [{ id: crypto.randomUUID(), startTime: "09:00 am", endTime: "05:00 pm" }] },
    { day: "FRI", isAvailable: true, timeSlots: [{ id: crypto.randomUUID(), startTime: "09:00 am", endTime: "05:00 pm" }] },
    { day: "SAT", isAvailable: false, timeSlots: [] },
  ]);

  // Toggle day availability
  const toggleDayAvailability = (dayIndex: number) => {
    const updatedSchedule = [...schedule];
    updatedSchedule[dayIndex].isAvailable = !updatedSchedule[dayIndex].isAvailable;

    // If day is marked as unavailable, clear time slots
    if (!updatedSchedule[dayIndex].isAvailable) {
      updatedSchedule[dayIndex].timeSlots = [];
    } else if (updatedSchedule[dayIndex].timeSlots.length === 0) {
      // If day is marked as available and has no time slots, add a default one
      updatedSchedule[dayIndex].timeSlots = [
        { id: crypto.randomUUID(), startTime: "09:00 am", endTime: "05:00 pm" }
      ];
    }
    setSchedule(updatedSchedule);
  };

  // Add a new time slot for a day
  const addTimeSlot = (dayIndex: number) => {
    const updatedSchedule = [...schedule];
    updatedSchedule[dayIndex].timeSlots.push({
      id: crypto.randomUUID(),
      startTime: "09:00 am",
      endTime: "05:00 pm"
    });
    setSchedule(updatedSchedule);
  };

  // Remove a time slot
  const removeTimeSlot = (dayIndex: number, slotId: string) => {
    const updatedSchedule = [...schedule];
    updatedSchedule[dayIndex].timeSlots = updatedSchedule[dayIndex].timeSlots.filter(
      slot => slot.id !== slotId
    );
    setSchedule(updatedSchedule);
  };

  // Update time slot values
  const updateTimeSlot = (dayIndex: number, slotId: string, field: 'startTime' | 'endTime', value: string) => {
    const updatedSchedule = [...schedule];
    const slotIndex = updatedSchedule[dayIndex].timeSlots.findIndex(slot => slot.id === slotId);
    if (slotIndex !== -1) {
      updatedSchedule[dayIndex].timeSlots[slotIndex][field] = value;
      setSchedule(updatedSchedule);
    }
  };

  // Handle form submission
  const handleSubmit = () => {
    console.log("Schedule data:", schedule);
    // TODO: Add API call to save schedule
  };

  return (
    <div className="w-full p-8" >      
      {/* Days of the week with time slots */}
      <div className="space-y-4 mb-8">
        {schedule.map((day, dayIndex) => (
          <div key={day.day} className="flex items-center gap-6">
            {/* Day checkbox */}
            <div className="flex items-center w-16">
              <Checkbox
                id={`day-${day.day}`}
                checked={day.isAvailable}
                onCheckedChange={() => toggleDayAvailability(dayIndex)}
                className="data-[state=checked]:bg-orange-1 data-[state=checked]:text-white data-[state=checked]:border-0 mr-2"
              />
              <Label htmlFor={`day-${day.day}`} className="font-medium text-gray-700">
                {day.day}
              </Label>
            </div>

            {/* Time slots or "Unavailable" text */}
            <div className="flex-1">
              {day.isAvailable ? (
                day.timeSlots.map((slot, slotIndex) => (
                  <div key={slot.id} className="flex items-center mb-2">
                    <div className="w-24">
                      <TimePicker
                        value={slot.startTime}
                        onChange={(value) => updateTimeSlot(dayIndex, slot.id, 'startTime', value)}
                      />
                    </div>
                    <span className="mx-2">-</span>
                    <div className="w-24">
                      <TimePicker
                        value={slot.endTime}
                        onChange={(value) => updateTimeSlot(dayIndex, slot.id, 'endTime', value)}
                      />
                    </div>

                    {slotIndex === 0 ? (
                      <Button
                        onClick={() => addTimeSlot(dayIndex)}
                        variant="ghost"
                        size="icon"
                        className="ml-2 text-orange-1"
                      >
                        <Plus className="h-4 w-4" />
                      </Button>
                    ) : (
                      <Button
                        onClick={() => removeTimeSlot(dayIndex, slot.id)}
                        variant="ghost"
                        size="icon"
                        className="ml-2 text-gray-500"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))
              ) : (
                <div
                  className="flex items-center h-10 cursor-pointer hover:bg-gray-50 rounded-md px-2 transition-colors"
                  onClick={() => toggleDayAvailability(dayIndex)}
                >
                  <span className="text-gray-500">Unavailable</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Save Button */}
      <Button
        onClick={handleSubmit}
        className="w-full md:w-auto bg-orange-1 hover:bg-orange-1/90"
      >
        Save Changes
      </Button>
    </div>
  );
}